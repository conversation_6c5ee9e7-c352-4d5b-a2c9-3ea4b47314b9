import { compare } from 'bcrypt-ts';
import NextAuth, { type DefaultSession, type NextAuthConfig } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { createGuestUser, getUser, createOrGetExternalUser } from '@/lib/db/queries';
import { authConfig } from './auth.config';
import { DUMMY_PASSWORD } from '@/lib/constants';

export type UserType = 'guest' | 'regular' | 'external';

declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string;
      type: UserType;
      externalId?: string;
      source?: string;
      token?: string;
      tenant?: string;
      userName?: string;
    } & DefaultSession['user'];
  }

  interface User {
    id?: string;
    email?: string | null;
    type?: UserType;
    externalId?: string;
    source?: string;
    token?: string;
    tenant?: string;
    userName?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    type: UserType;
    externalId?: string;
    source?: string;
    token?: string;
    tenant?: string;
    userName?: string;
  }
}

export const authOptions = {
  ...authConfig,
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        const users = await getUser(email);

        if (users.length === 0) {
          await compare(password, DUMMY_PASSWORD);
          return null;
        }

        const [user] = users;

        if (!user.password) {
          await compare(password, DUMMY_PASSWORD);
          return null;
        }

        const passwordsMatch = await compare(password, user.password);

        if (!passwordsMatch) return null;

        return {
          id: user.id,
          email: user.email,
          type: 'regular' as UserType,
          externalId: user.externalId || undefined,
          source: user.source || undefined
        };
      },
    }),
    Credentials({
      id: 'guest',
      credentials: {},
      async authorize() {
        const [guestUser] = await createGuestUser();
        return {
          id: guestUser.id,
          email: guestUser.email,
          type: 'guest' as UserType,
        };
      },
    }),

  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.type = user.type as UserType;
        if (user.externalId) token.externalId = user.externalId;
        if (user.source) token.source = user.source;
        if (user.token) token.token = user.token;
        if (user.tenant) token.tenant = user.tenant;
        if (user.userName) token.userName = user.userName;
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.type = token.type;
        if (token.externalId) session.user.externalId = token.externalId;
        if (token.source) session.user.source = token.source;
        if (token.token) session.user.token = token.token;
        if (token.tenant) session.user.tenant = token.tenant;
        if (token.userName) session.user.userName = token.userName;
      }

      return session;
    },
  },
} satisfies NextAuthConfig;

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth(authOptions);
