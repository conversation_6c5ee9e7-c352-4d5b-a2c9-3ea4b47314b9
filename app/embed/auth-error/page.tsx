interface AuthErrorPageProps {
  searchParams: Promise<{
    error?: string;
  }>;
}

const errorMessages = {
  missing_auth: 'Authentication required. Please provide user_id and source parameters.',
  auth_failed: 'Authentication failed. Please check your credentials.',
  invalid_origin: 'Request from unauthorized origin.',
} as const;

export default async function AuthErrorPage({ searchParams }: AuthErrorPageProps) {
  // Await searchParams to comply with Next.js 15 async API
  const params = await searchParams;
  const { error = 'auth_failed' } = params;
  const message = errorMessages[error as keyof typeof errorMessages] || errorMessages.auth_failed;

  return (
    <div className="h-screen flex items-center justify-center bg-background">
      <div className="max-w-md mx-auto p-6 text-center">
        <div className="size-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg
            className="size-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 19c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        <h1 className="text-xl font-semibold text-gray-900 mb-2">
          Authentication Error
        </h1>

        <p className="text-gray-600 mb-6">
          {message}
        </p>

        <div className="text-sm text-gray-500">
          <p>If you&apos;re the site owner:</p>
          <ul className="mt-2 space-y-1 text-left">
            <li>• Check that user_id and source parameters are provided</li>
            <li>• Verify your domain is authorized</li>
            <li>• Ensure the URL format: /embed?user_id=USER_ID&source=SOURCE</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
