'use client';

import { useState, useEffect } from 'react';

export default function TestPage() {
  const [userId, setUserId] = useState('test-user-123');
  const [source, setSource] = useState('test-site');
  const [origin, setOrigin] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setOrigin(window.location.origin);
    }
  }, []);

  const embedUrlWithParams = `/embed?user_id=${encodeURIComponent(userId)}&source=${encodeURIComponent(source)}`;

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">iframe Integration Test</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Configuration Panel */}
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Configuration</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">User ID</label>
                <input
                  type="text"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="Enter user ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Source</label>
                <input
                  type="text"
                  value={source}
                  onChange={(e) => setSource(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="Enter source identifier"
                />
              </div>


            </div>
          </div>

          {/* Integration Examples */}
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4">Integration Examples</h3>

            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">Simple Parameters Authentication</h4>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
{`<iframe
  src="${origin}/embed?user_id=${userId}&source=${source}"
  width="100%"
  height="600px">
</iframe>`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Live Preview */}
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">iframe Preview</h2>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Simple Parameters Authentication:</h3>
                <iframe
                  src={embedUrlWithParams}
                  width="100%"
                  height="400px"
                  className="border rounded"
                  title="Simple Auth Test"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
