'use client';

import { useMemo, useOptimistic, useState } from 'react';
import type { Button } from '@/components/ui/button';
import { chatModels } from '@/lib/ai/models';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import type { Session } from 'next-auth';

export function ModelSelector({
  session,
  selectedModelId,
  className,
}: {
  session: Session;
  selectedModelId: string;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId);

  const userType = session.user.type;
  let entitlements = entitlementsByUserType[userType];

  // Fallback nếu không tìm thấy entitlements cho userType
  if (!entitlements) {
    console.warn(
      `No entitlements found for user type: ${userType}, falling back to guest`,
    );
    entitlements = entitlementsByUserType.guest;
  }

  const { availableChatModelIds } = entitlements;

  const availableChatModels = useMemo(
    () =>
      chatModels.filter((chatModel) =>
        availableChatModelIds.includes(chatModel.id),
      ),
    [availableChatModelIds],
  );

  const selectedChatModel = useMemo(
    () =>
      availableChatModels.find(
        (chatModel) => chatModel.id === optimisticModelId,
      ),
    [optimisticModelId, availableChatModels],
  );

  // Nếu chỉ có 1 model, không hiển thị gì
  if (availableChatModels.length <= 1) {
    return null;
  }

  // TODO: Implement full model selector UI
  return null;
}
