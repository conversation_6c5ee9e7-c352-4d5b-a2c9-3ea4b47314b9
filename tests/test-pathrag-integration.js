#!/usr/bin/env node

/**
 * Script để test PathRAG integration sau migration
 */

const readline = require('node:readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔄 TESTING PATHRAG INTEGRATION');
console.log('================================\n');

// Test 1: Kiểm tra PathRAG API
async function testPathRAGAPI() {
  console.log('1️⃣ Testing PathRAG API...');
  
  try {
    const response = await fetch('http://localhost:8123/api/context', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'PathRAG là gì',
        max_results: 5
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ PathRAG API is working');
      console.log(`   Query: ${data.data?.query || 'N/A'}`);
      console.log(`   Total documents: ${data.data?.total_documents || 0}`);
      console.log(`   Response length: ${data.data?.context_documents?.length || 0} documents`);
      console.log(`   Processing type: ${data.data?.processing_type || 'unknown'}`);
    } else {
      console.log('❌ PathRAG API returned error:', data.message);
    }
  } catch (error) {
    console.log('❌ PathRAG API failed:', error.message);
  }
  console.log('');
}

// Test 2: Kiểm tra Next.js server
async function testNextJSServer() {
  console.log('2️⃣ Testing Next.js server...');
  
  try {
    const response = await fetch('http://localhost:3000/api/health', {
      method: 'GET'
    });

    if (response.status === 404) {
      console.log('ℹ️  Health endpoint not found (normal for this project)');
    } else if (response.ok) {
      console.log('✅ Next.js server is healthy');
    } else {
      console.log(`⚠️  Next.js server responded with ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Next.js server failed:', error.message);
  }
  console.log('');
}

// Test 3: Kiểm tra environment variables
function testEnvironmentVariables() {
  console.log('3️⃣ Testing environment variables...');
  
  const requiredVars = [
    'PATHRAG_API_URL',
    'GOOGLE_GENERATIVE_AI_API_KEY',
    'POSTGRES_URL',
    'AUTH_SECRET'
  ];

  const missingVars = [];
  const presentVars = [];

  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      presentVars.push(varName);
    } else {
      missingVars.push(varName);
    }
  });

  console.log(`✅ Present: ${presentVars.join(', ')}`);
  if (missingVars.length > 0) {
    console.log(`❌ Missing: ${missingVars.join(', ')}`);
  }
  console.log('');
}

// Test 4: Tạo test request tới chat API (nếu user muốn)
async function testChatAPI() {
  console.log('4️⃣ Would you like to test the chat API with PathRAG? (y/n)');
  
  return new Promise((resolve) => {
    rl.question('Enter choice: ', async (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        console.log('📝 To test chat API with PathRAG:');
        console.log('   1. Make sure PathRAG API is running: python pathrag/pathrag_api.py');
        console.log('   2. Open http://localhost:3000 in your browser');
        console.log('   3. Try asking: "PathRAG hoạt động như thế nào?"');
        console.log('   4. Check DevTools Console for PathRAG API logs');
        console.log('   5. Verify the response comes from PathRAG knowledge base');
        console.log('   6. Look for: "🔍 PathRAG API Request:" in console');
      }
      resolve();
    });
  });
}

// Load environment variables
require('dotenv').config({ path: '/Users/<USER>/DotB/cs_agent/.env' });

// Run all tests
async function runTests() {
  testEnvironmentVariables();
  await testPathRAGAPI();
  await testNextJSServer();
  await testChatAPI();
  
  console.log('🎉 PathRAG integration testing completed!');
  console.log('\n📋 Summary:');
  console.log('- ✅ PathRAG API endpoint updated to localhost:8123/api/context');
  console.log('- ✅ PathRAG tool has enhanced error handling and logging');
  console.log('- ✅ Environment variables cleaned up (legacy ones commented)');
  console.log('- ✅ Test script now targets correct PathRAG API');
  console.log('\n🚀 Improvements made:');
  console.log('- Better response validation and error messages');
  console.log('- Detailed console logging for debugging');
  console.log('- Support for flexible document formats');
  console.log('- Enhanced TypeScript types and interfaces');
  console.log('\n🔧 To test the system:');
  console.log('- Start PathRAG API: cd pathrag && python pathrag_api.py');
  console.log('- Start Chat SDK: pnpm dev');
  console.log('- Test integration: node test-pathrag-integration.js');
  
  rl.close();
}

runTests().catch(console.error);
